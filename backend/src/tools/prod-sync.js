#!/usr/bin/env node
/**
 * 生产数据单向同步工具
 * 安全地从生产环境同步数据到开发环境
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

// 配置
const CONFIG = {
  production: {
    host: 'root@************',
    key: '/Users/<USER>/.ssh/vps_key',
    dbPath: '/var/www/dstatus-backend/data/license.db',
    backupPath: '/var/backups/litestream/license.db'
  },
  local: {
    dbPath: path.join(__dirname, '../../data/license.db'),
    tempPath: path.join(__dirname, '../../data/temp-sync.db'),
    backupDir: path.join(__dirname, '../../data/sync-backups')
  },
  sync: {
    interval: 300000, // 5分钟
    autoSanitize: true,
    watchMode: false
  }
};

class ProductionSync {
  constructor(options = {}) {
    this.config = { ...CONFIG, ...options };
    this.isRunning = false;
    
    // 确保目录存在
    if (!fs.existsSync(this.config.local.backupDir)) {
      fs.mkdirSync(this.config.local.backupDir, { recursive: true });
    }
  }

  /**
   * 使用 rsync 单向同步（更高效）
   */
  async syncWithRsync() {
    console.log('🔄 使用 rsync 同步数据库...');
    
    try {
      // 先备份本地
      await this.backupLocal();
      
      // 使用 rsync 同步
      const rsyncCmd = `rsync -avz --checksum -e "ssh -i ${this.config.production.key}" ${this.config.production.host}:${this.config.production.dbPath}* ${path.dirname(this.config.local.dbPath)}/`;
      
      console.log('📥 同步中...');
      execSync(rsyncCmd, { stdio: 'inherit' });
      
      // 数据脱敏
      if (this.config.sync.autoSanitize) {
        await this.sanitizeLocalData();
      }
      
      console.log('✅ 同步完成！');
      
    } catch (error) {
      console.error('❌ 同步失败:', error.message);
      // 恢复备份
      await this.restoreLatestBackup();
    }
  }

  /**
   * 监听模式 - 定期同步
   */
  async watchMode() {
    if (this.isRunning) {
      console.log('⚠️  监听模式已在运行');
      return;
    }
    
    this.isRunning = true;
    console.log('👁️  启动监听模式...');
    console.log(`📍 同步间隔: ${this.config.sync.interval / 1000}秒`);
    console.log('按 Ctrl+C 停止\n');
    
    // 首次同步
    await this.syncWithRsync();
    
    // 定期同步
    this.syncInterval = setInterval(async () => {
      console.log(`\n🔄 [${new Date().toLocaleTimeString()}] 执行定期同步...`);
      await this.syncWithRsync();
    }, this.config.sync.interval);
    
    // 处理退出
    process.on('SIGINT', () => {
      console.log('\n👋 停止监听模式...');
      clearInterval(this.syncInterval);
      this.isRunning = false;
      process.exit(0);
    });
  }

  /**
   * 数据脱敏
   */
  async sanitizeLocalData() {
    console.log('🔒 执行数据脱敏...');
    
    const db = new sqlite3.Database(this.config.local.dbPath);
    
    return new Promise((resolve, reject) => {
      db.serialize(() => {
        // 开始事务
        db.run('BEGIN TRANSACTION');
        
        // 脱敏用户密码和邮箱
        db.run(`
          UPDATE users 
          SET password = '$2b$10$dummyHashForDevelopment',
              email = username || '@dev.local'
          WHERE role != 'admin'
        `);
        
        // 脱敏许可证密钥
        db.run(`
          UPDATE licenses 
          SET instance_secret = 'dev-secret-' || id
        `);
        
        // 清理敏感日志
        db.run(`DELETE FROM audit_logs WHERE created_at < datetime('now', '-7 days')`);
        
        // 提交事务
        db.run('COMMIT', (err) => {
          db.close();
          if (err) {
            console.error('❌ 脱敏失败:', err);
            reject(err);
          } else {
            console.log('✅ 数据脱敏完成');
            resolve();
          }
        });
      });
    });
  }

  /**
   * 备份本地数据库
   */
  async backupLocal() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(this.config.local.backupDir, `sync-backup-${timestamp}.db`);
    
    if (fs.existsSync(this.config.local.dbPath)) {
      fs.copyFileSync(this.config.local.dbPath, backupFile);
      console.log(`💾 本地备份: ${path.basename(backupFile)}`);
      
      // 清理旧备份
      this.cleanOldBackups();
    }
  }

  /**
   * 清理旧备份
   */
  cleanOldBackups() {
    const files = fs.readdirSync(this.config.local.backupDir)
      .filter(f => f.startsWith('sync-backup-'))
      .sort()
      .reverse();
    
    // 保留最近10个备份
    files.slice(10).forEach(file => {
      fs.unlinkSync(path.join(this.config.local.backupDir, file));
    });
  }

  /**
   * 恢复最新备份
   */
  async restoreLatestBackup() {
    const files = fs.readdirSync(this.config.local.backupDir)
      .filter(f => f.startsWith('sync-backup-'))
      .sort()
      .reverse();
    
    if (files.length > 0) {
      const latestBackup = path.join(this.config.local.backupDir, files[0]);
      fs.copyFileSync(latestBackup, this.config.local.dbPath);
      console.log('✅ 已从备份恢复');
    }
  }

  /**
   * 检查同步状态
   */
  async checkStatus() {
    console.log('📊 同步状态检查\n');
    
    // 本地信息
    if (fs.existsSync(this.config.local.dbPath)) {
      const stats = fs.statSync(this.config.local.dbPath);
      console.log('本地数据库:');
      console.log(`  最后修改: ${stats.mtime}`);
      console.log(`  大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
      
      // 检查记录数
      const db = new sqlite3.Database(this.config.local.dbPath);
      db.get('SELECT COUNT(*) as users FROM users', (err, row) => {
        if (!err) console.log(`  用户数: ${row.users}`);
      });
      db.get('SELECT COUNT(*) as licenses FROM licenses', (err, row) => {
        if (!err) console.log(`  许可证数: ${row.licenses}`);
        db.close();
      });
    }
    
    // 远程信息
    try {
      const remoteInfo = execSync(
        `ssh -i ${this.config.production.key} ${this.config.production.host} "stat -c '%Y %s' ${this.config.production.dbPath}"`,
        { encoding: 'utf8' }
      ).trim().split(' ');
      
      const remoteMtime = new Date(parseInt(remoteInfo[0]) * 1000);
      const remoteSize = parseInt(remoteInfo[1]);
      
      console.log('\n生产数据库:');
      console.log(`  最后修改: ${remoteMtime}`);
      console.log(`  大小: ${(remoteSize / 1024 / 1024).toFixed(2)} MB`);
    } catch (error) {
      console.log('\n❌ 无法获取生产数据库信息');
    }
    
    // 备份信息
    const backups = fs.readdirSync(this.config.local.backupDir)
      .filter(f => f.startsWith('sync-backup-'));
    console.log(`\n本地备份: ${backups.length} 个`);
    
    // 同步建议
    console.log('\n建议:');
    console.log('- 使用 watch 模式进行持续同步');
    console.log('- 定期检查数据脱敏是否正常');
    console.log('- 避免在生产高峰期同步');
  }
}

// CLI
if (require.main === module) {
  const sync = new ProductionSync();
  const command = process.argv[2];
  
  const commands = {
    sync: () => sync.syncWithRsync(),
    watch: () => sync.watchMode(),
    status: () => sync.checkStatus(),
    sanitize: () => sync.sanitizeLocalData(),
    help: () => {
      console.log('生产数据单向同步工具\n');
      console.log('用法:');
      console.log('  node prod-sync.js sync      - 执行一次同步');
      console.log('  node prod-sync.js watch     - 监听模式（定期同步）');
      console.log('  node prod-sync.js status    - 查看同步状态');
      console.log('  node prod-sync.js sanitize  - 仅执行数据脱敏');
      console.log('  node prod-sync.js help      - 显示帮助');
      console.log('\n特性:');
      console.log('- 单向同步（生产→开发）');
      console.log('- 自动数据脱敏');
      console.log('- 定期同步模式');
      console.log('- 自动备份和恢复');
    }
  };
  
  const cmd = commands[command] || commands.help;
  cmd().catch(console.error);
}

module.exports = ProductionSync;