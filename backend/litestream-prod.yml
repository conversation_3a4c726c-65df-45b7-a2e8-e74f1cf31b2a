# Litestream 生产环境配置
# 请设置环境变量：LITESTREAM_ACCESS_KEY_ID 和 LITESTREAM_SECRET_ACCESS_KEY

dbs:
  - path: /var/www/dstatus-backend/data/license.db
    
    # 本地文件备份（主备份）
    replicas:
      - type: file
        path: /var/backups/litestream/license.db
        retention: 168h  # 保留7天
        retention-check-interval: 1h
        sync-interval: 1s  # 实时同步
        
      # S3 备份（需要配置）
      # - type: s3
      #   bucket: your-bucket-name
      #   path: dstatus/license.db
      #   region: us-east-1
      #   access-key-id: ${LITESTREAM_ACCESS_KEY_ID}
      #   secret-access-key: ${LITESTREAM_SECRET_ACCESS_KEY}
      #   retention: 720h  # 保留30天
      #   retention-check-interval: 24h
      
      # 阿里云 OSS 备份（需要配置）
      # - type: s3
      #   bucket: your-oss-bucket
      #   path: dstatus/license.db
      #   endpoint: oss-cn-hongkong.aliyuncs.com
      #   access-key-id: ${LITESTREAM_ACCESS_KEY_ID}
      #   secret-access-key: ${LITESTREAM_SECRET_ACCESS_KEY}
      #   retention: 720h  # 保留30天
      #   retention-check-interval: 24h

# 监控配置
addr: ":9090"