"use strict";

const BaseAdapter = require("./base");
const SQLiteAdapter = require("./sqlite");
const PostgresAdapter = require("./postgresql");

/**
 * 双数据库代理适配器
 * 写操作使用SQLite（快速可靠）
 * 读操作优先PostgreSQL，失败时降级到SQLite
 */
class DualAdapter extends BaseAdapter {
    constructor(sqliteConfig, postgresConfig) {
        super(sqliteConfig);
        this.type = 'dual';
        
        // 创建两个适配器实例
        this.sqliteAdapter = new SQLiteAdapter(sqliteConfig);
        this.postgresAdapter = new PostgresAdapter(postgresConfig);
        
        // 状态管理
        this.postgresAvailable = true;
        this.sqliteConnected = false;
        this.postgresConnected = false;
        
        // 错误计数器（用于降级决策）
        this.postgresErrorCount = 0;
        this.maxErrors = 3;

        // 数据表实时性分类
        this.dataCategories = {
            // 高实时性：立即双向同步
            realtime: ['setting', 'servers', 'groups'],
            // 低实时性：SQLite写入，定期同步
            historical: ['load_m', 'load_h', 'load_archive', 'traffic', 'lt'],
            // 只读历史：优先PostgreSQL读取
            readonly: ['ai_reports', 'ssh_scripts']
        };
    }

    async connect() {
        // 总是连接SQLite（写入数据库）
        try {
            await this.sqliteAdapter.connect();
            this.sqliteConnected = true;
            console.log("[数据库] SQLite 连接成功（写入数据库）");
        } catch (error) {
            console.error("[数据库] SQLite 连接失败:", error.message);
            throw error; // SQLite连接失败是致命的
        }

        // 尝试连接PostgreSQL（读取数据库）
        try {
            await this.postgresAdapter.connect();
            this.postgresConnected = true;
            this.postgresAvailable = true;
            this.postgresErrorCount = 0;
            console.log("[数据库] PostgreSQL 连接成功（读取数据库）");
            
            // 启动PostgreSQL连接池监控
            if (this.postgresAdapter.startPoolMonitoring) {
                this.postgresAdapter.startPoolMonitoring(60000);
            }
        } catch (error) {
            console.warn("[数据库] PostgreSQL 连接失败，降级到SQLite读取:", error.message);
            this.postgresConnected = false;
            this.postgresAvailable = false;
            this.postgresErrorCount = this.maxErrors; // 直接标记为不可用
        }
    }

    async disconnect() {
        const promises = [];
        
        if (this.sqliteConnected && this.sqliteAdapter.disconnect) {
            promises.push(this.sqliteAdapter.disconnect());
        }
        
        if (this.postgresConnected && this.postgresAdapter.disconnect) {
            promises.push(this.postgresAdapter.disconnect());
        }

        await Promise.allSettled(promises);
        console.log("[数据库] 双数据库连接已关闭");
    }

    /**
     * 检查PostgreSQL健康状态并决定是否降级
     */
    _checkPostgresHealth(error) {
        if (error && error.message && error.message.includes('timeout exceeded when trying to connect')) {
            this.postgresErrorCount++;
            console.warn(`[数据库] PostgreSQL错误计数: ${this.postgresErrorCount}/${this.maxErrors}`);
            
            if (this.postgresErrorCount >= this.maxErrors) {
                this.postgresAvailable = false;
                console.warn("[数据库] PostgreSQL已降级，使用SQLite进行读取");
            }
        }
    }

    /**
     * 写操作：总是使用SQLite，但对关键表进行智能同步
     */
    async run(sql, params = []) {
        if (!this.sqliteConnected) {
            throw new Error("SQLite数据库未连接");
        }

        // 智能同步：检查是否为关键表的UPSERT操作
        await this._smartSyncBeforeWrite(sql, params);

        // 修复SQL语法兼容性：PostgreSQL -> SQLite
        const { sql: adaptedSql, params: adaptedParams } = this._adaptUpsertSyntax(sql, params);
        
        return await this.sqliteAdapter.run(adaptedSql, adaptedParams);
    }

    /**
     * 读操作：基于数据实时性的智能读取策略
     */
    async get(sql, params = []) {
        const tableCategory = this._getTableCategory(sql);
        
        // 策略1：只读历史表 - 优先PostgreSQL
        if (tableCategory === 'readonly' && this.postgresAvailable && this.postgresConnected) {
            try {
                const pgResult = await this.postgresAdapter.get(sql, params);
                if (pgResult !== undefined && pgResult !== null) {
                    return pgResult;
                }
            } catch (error) {
                this._checkPostgresHealth(error);
                console.warn("[数据库] PostgreSQL只读表查询失败，降级到SQLite:", error.message);
            }
        }
        
        // 策略2：高实时性表 - SQLite优先，PostgreSQL补充  
        if (tableCategory === 'realtime') {
            const { sql: adaptedSql, params: adaptedParams } = this._adaptUpsertSyntax(sql, params);
            const sqliteResult = await this.sqliteAdapter.get(adaptedSql, adaptedParams);
            
            if (this.postgresAvailable && this.postgresConnected) {
                try {
                    const pgResult = await this.postgresAdapter.get(sql, params);
                    
                    // SQLite有数据 = 最新，优先返回
                    if (sqliteResult !== undefined && sqliteResult !== null) {
                        // 异步同步到PostgreSQL
                        if (!pgResult || JSON.stringify(pgResult) !== JSON.stringify(sqliteResult)) {
                            this._asyncSyncToPostgreSQL(sql, params, sqliteResult);
                        }
                        return sqliteResult;
                    }
                    
                    // SQLite无数据，使用PostgreSQL历史数据
                    if (pgResult !== undefined && pgResult !== null) {
                        return pgResult;
                    }
                } catch (error) {
                    this._checkPostgresHealth(error);
                    console.warn("[数据库] PostgreSQL实时表查询失败，使用SQLite:", error.message);
                }
            }
            
            return sqliteResult;
        }
        
        // 策略3：历史数据表 - 主要使用SQLite
        const { sql: adaptedSql, params: adaptedParams } = this._adaptUpsertSyntax(sql, params);
        return await this.sqliteAdapter.get(adaptedSql, adaptedParams);
    }

    /**
     * 批量读操作：基于数据实时性的智能读取策略
     */
    async all(sql, params = []) {
        const tableCategory = this._getTableCategory(sql);
        
        // 策略1：只读历史表 - 优先PostgreSQL
        if (tableCategory === 'readonly' && this.postgresAvailable && this.postgresConnected) {
            try {
                const pgResult = await this.postgresAdapter.all(sql, params);
                if (pgResult && pgResult.length > 0) {
                    return pgResult;
                }
            } catch (error) {
                this._checkPostgresHealth(error);
                console.warn("[数据库] PostgreSQL只读表批量查询失败，降级到SQLite:", error.message);
            }
        }
        
        // 策略2：高实时性表 - SQLite优先
        if (tableCategory === 'realtime') {
            const { sql: adaptedSql, params: adaptedParams } = this._adaptUpsertSyntax(sql, params);
            const sqliteResult = await this.sqliteAdapter.all(adaptedSql, adaptedParams);
            
            if (this.postgresAvailable && this.postgresConnected) {
                try {
                    const pgResult = await this.postgresAdapter.all(sql, params);
                    
                    // SQLite有数据，返回SQLite数据（最新）
                    if (sqliteResult && sqliteResult.length > 0) {
                        return sqliteResult;
                    }
                    
                    // SQLite无数据，返回PostgreSQL历史数据
                    return pgResult || [];
                } catch (error) {
                    this._checkPostgresHealth(error);
                    console.warn("[数据库] PostgreSQL实时表批量查询失败，使用SQLite:", error.message);
                }
            }
            
            return sqliteResult || [];
        }
        
        // 策略3：历史数据表 - 主要使用SQLite，减少PostgreSQL负载
        const { sql: adaptedSql, params: adaptedParams } = this._adaptUpsertSyntax(sql, params);
        return await this.sqliteAdapter.all(adaptedSql, adaptedParams) || [];
    }

    /**
     * 查询操作：与all相同的逻辑
     */
    async query(sql, params = []) {
        return await this.all(sql, params);
    }

    /**
     * SQLite特有方法：直接路由到SQLite适配器
     */
    pragma(statement) {
        if (this.sqliteAdapter && this.sqliteAdapter.db && this.sqliteAdapter.db.pragma) {
            return this.sqliteAdapter.db.pragma(statement);
        }
        console.warn("[数据库] pragma方法不可用（SQLite未连接）");
        return null;
    }

    /**
     * PostgreSQL特有方法：路由到PostgreSQL适配器
     */
    startPoolMonitoring(interval) {
        if (this.postgresConnected && this.postgresAdapter.startPoolMonitoring) {
            return this.postgresAdapter.startPoolMonitoring(interval);
        }
    }

    stopPoolMonitoring() {
        if (this.postgresConnected && this.postgresAdapter.stopPoolMonitoring) {
            return this.postgresAdapter.stopPoolMonitoring();
        }
    }

    /**
     * 事务方法：使用SQLite（因为写操作在SQLite）
     */
    async beginTransaction() {
        if (!this.sqliteConnected) {
            throw new Error("SQLite数据库未连接");
        }
        return await this.sqliteAdapter.beginTransaction();
    }

    async commitTransaction() {
        if (!this.sqliteConnected) {
            throw new Error("SQLite数据库未连接");
        }
        return await this.sqliteAdapter.commitTransaction();
    }

    async rollbackTransaction() {
        if (!this.sqliteConnected) {
            throw new Error("SQLite数据库未连接");
        }
        return await this.sqliteAdapter.rollbackTransaction();
    }

    /**
     * 识别SQL查询涉及的表类别
     */
    _getTableCategory(sql) {
        const sqlLower = sql.toLowerCase();
        
        // 检查实时性表
        for (const table of this.dataCategories.realtime) {
            if (sqlLower.includes(`from ${table}`) || sqlLower.includes(`into ${table}`) || 
                sqlLower.includes(`update ${table}`) || sqlLower.includes(`${table} `)) {
                return 'realtime';
            }
        }
        
        // 检查历史数据表
        for (const table of this.dataCategories.historical) {
            if (sqlLower.includes(`from ${table}`) || sqlLower.includes(`into ${table}`) || 
                sqlLower.includes(`update ${table}`) || sqlLower.includes(`${table} `)) {
                return 'historical';
            }
        }
        
        // 检查只读表
        for (const table of this.dataCategories.readonly) {
            if (sqlLower.includes(`from ${table}`) || sqlLower.includes(`into ${table}`) || 
                sqlLower.includes(`update ${table}`) || sqlLower.includes(`${table} `)) {
                return 'readonly';
            }
        }
        
        // 默认为实时性表（保守策略）
        return 'realtime';
    }

    /**
     * 智能同步：基于表类别的写入前同步检查
     */
    async _smartSyncBeforeWrite(sql, params = []) {
        const tableCategory = this._getTableCategory(sql);
        const isUpsert = sql.includes('ON CONFLICT') || sql.includes('INSERT OR REPLACE');
        
        // 只对高实时性表的UPSERT操作进行同步检查
        if (tableCategory !== 'realtime' || !isUpsert || !this.postgresAvailable || !this.postgresConnected) {
            return;
        }

        try {
            // 针对setting表的特殊处理
            if (sql.toLowerCase().includes('into setting')) {
                await this._syncSettingRecord(params[0]);
            }
            // 针对servers表的处理
            else if (sql.toLowerCase().includes('into servers')) {
                console.log(`[数据库] 智能同步servers记录: ${params[0]}`);
                // 可以添加servers表的同步逻辑
            }
        } catch (error) {
            console.warn('[数据库] 智能同步失败，继续写入操作:', error.message);
        }
    }

    /**
     * 同步setting表的单条记录
     */
    async _syncSettingRecord(key) {
        if (!key) return;

        try {
            // 从PostgreSQL读取最新数据
            const pgRecord = await this.postgresAdapter.get(
                "SELECT * FROM setting WHERE key=$1", [key]
            );
            
            if (pgRecord) {
                // 将PostgreSQL的数据写入SQLite
                await this.sqliteAdapter.run(
                    "INSERT OR REPLACE INTO setting (key, val) VALUES (?, ?)",
                    [pgRecord.key, pgRecord.val]
                );
                console.log(`[数据库] 智能同步setting记录: ${key}`);
            }
        } catch (error) {
            console.warn(`[数据库] 同步setting记录失败 ${key}:`, error.message);
        }
    }

    /**
     * 异步反向同步：将SQLite数据同步到PostgreSQL
     */
    _asyncSyncToPostgreSQL(sql, params, sqliteData) {
        // 异步执行，不阻塞当前操作
        setImmediate(async () => {
            try {
                // 检查是否为setting表查询
                if (sql.toLowerCase().includes('from setting') && sqliteData && sqliteData.key) {
                    await this.postgresAdapter.run(
                        "INSERT INTO setting (key, val) VALUES ($1, $2) ON CONFLICT(key) DO UPDATE SET val = $3",
                        [sqliteData.key, sqliteData.val, sqliteData.val]
                    );
                    console.log(`[数据库] 反向同步setting到PostgreSQL: ${sqliteData.key}`);
                }
                
                // 🆕 新增：记录其他表的同步需求（只记录，不执行）
                else if (sql.toLowerCase().includes('from servers') && sqliteData && sqliteData.sid) {
                    console.log(`[数据库] 检测到servers同步需求: ${sqliteData.sid} (暂不执行)`);
                    this._recordSyncNeed('servers', sqliteData.sid, sqliteData);
                }
                else if (sql.toLowerCase().includes('from groups') && sqliteData && sqliteData.id) {
                    console.log(`[数据库] 检测到groups同步需求: ${sqliteData.id} (暂不执行)`);
                    this._recordSyncNeed('groups', sqliteData.id, sqliteData);
                }
            } catch (error) {
                // 🆕 改进：详细错误日志
                console.error(`[数据库] 同步检查失败: ${error.message}`, {
                    sql: sql.substring(0, 100),
                    error: error.stack
                });
            }
        });
    }

    /**
     * 适配UPSERT语法：PostgreSQL -> SQLite
     */
    _adaptUpsertSyntax(sql, params) {
        let adaptedSql = sql;
        let adaptedParams = [...params]; // 创建参数副本

        // 处理setting表的UPSERT语法
        if (sql.includes('INSERT INTO setting') && sql.includes('ON CONFLICT(key)')) {
            // PostgreSQL: INSERT INTO setting (key, val) VALUES ($1, $2) ON CONFLICT(key) DO UPDATE SET val = $3
            // SQLite: INSERT OR REPLACE INTO setting (key, val) VALUES (?, ?)
            adaptedSql = sql
                .replace(/INSERT INTO setting \(key, val\) VALUES \(\$1, \$2\)\s*ON CONFLICT\(key\) DO UPDATE SET val = \$3/gi,
                         'INSERT OR REPLACE INTO setting (key, val) VALUES (?, ?)')
                .replace(/\$(\d+)/g, '?');
            
            // 调整参数数组：原来是[key, val, val]，现在只需要[key, val]
            if (adaptedParams.length >= 3) {
                adaptedParams = [adaptedParams[0], adaptedParams[1]];
            }
        }
        // 处理其他ON CONFLICT语法
        else if (sql.includes('ON CONFLICT') && sql.includes('DO NOTHING')) {
            adaptedSql = sql
                .replace(/INSERT INTO ([^(]+)/gi, 'INSERT OR IGNORE INTO $1')
                .replace(/\s+ON CONFLICT[^;]*/gi, '')
                .replace(/\$(\d+)/g, '?');
        }
        // 3. 处理OFFSET语法兼容性
        else if (sql.includes('OFFSET') && !sql.includes('LIMIT')) {
            // PostgreSQL: ORDER BY xxx OFFSET n
            // SQLite: ORDER BY xxx LIMIT -1 OFFSET n
            adaptedSql = sql.replace(/(\s+ORDER\s+BY[^O]+?)OFFSET\s+(\$\d+|\?)/gi, '$1LIMIT -1 OFFSET $2');
        }
        // 4. 处理PostgreSQL特有函数
        else if (sql.includes('EXTRACT(EPOCH FROM NOW())')) {
            adaptedSql = sql.replace(/EXTRACT\(EPOCH FROM NOW\(\)\)::INTEGER/gi, "strftime('%s', 'now')");
        }
        
        // 5. 通用参数占位符转换
        if (sql.includes('$')) {
            adaptedSql = adaptedSql.replace(/\$(\d+)/g, '?');
        }

        return { sql: adaptedSql, params: adaptedParams };
    }

    /**
     * 🆕 新增：同步需求记录（纯监控，无副作用）
     */
    _recordSyncNeed(table, id, data) {
        try {
            // 只记录到内存统计，不写文件，不执行同步
            if (!this.syncStats) this.syncStats = {};
            if (!this.syncStats[table]) this.syncStats[table] = 0;
            this.syncStats[table]++;
            
            // 记录最近的同步需求（保留最新10条）
            if (!this.recentSyncNeeds) this.recentSyncNeeds = [];
            this.recentSyncNeeds.push({
                table,
                id,
                timestamp: Date.now(),
                dataKeys: data ? Object.keys(data) : []
            });
            
            // 只保留最近10条记录
            if (this.recentSyncNeeds.length > 10) {
                this.recentSyncNeeds = this.recentSyncNeeds.slice(-10);
            }
        } catch (error) {
            // 记录失败也不影响主流程
            console.warn('[数据库] 记录同步需求失败:', error.message);
        }
    }

    /**
     * 获取当前数据库状态信息
     */
    getStatus() {
        return {
            type: 'dual',
            sqlite: {
                connected: this.sqliteConnected,
                role: 'write'
            },
            postgresql: {
                connected: this.postgresConnected,
                available: this.postgresAvailable,
                errorCount: this.postgresErrorCount,
                role: 'read'
            },
            // 🆕 新增：同步监控统计
            syncMonitoring: {
                stats: this.syncStats || {},
                recentNeeds: this.recentSyncNeeds || [],
                totalDetected: Object.values(this.syncStats || {}).reduce((a, b) => a + b, 0)
            }
        };
    }
}

module.exports = DualAdapter;