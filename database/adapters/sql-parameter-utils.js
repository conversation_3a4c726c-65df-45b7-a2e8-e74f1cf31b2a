"use strict";

/**
 * SQL参数处理工具类
 * 提供统一的参数格式转换和处理机制
 */
class SqlParameterUtils {
    /**
     * 解析SQL中的参数占位符
     * @param {string} sql - SQL语句
     * @returns {Object} - 解析结果
     */
    static parseParameters(sql) {
        // 匹配 $1, $2, $3... 格式的参数
        const pgParams = sql.match(/\$(\d+)/g) || [];
        const paramNumbers = pgParams.map(p => parseInt(p.substring(1)));
        
        return {
            originalSql: sql,
            pgParameters: pgParams,
            paramNumbers: paramNumbers,
            maxParamIndex: paramNumbers.length > 0 ? Math.max(...paramNumbers) : 0,
            hasParameters: pgParams.length > 0
        };
    }

    /**
     * 将PostgreSQL风格的SQL转换为SQLite风格
     * @param {string} sql - 原始SQL
     * @param {Array} params - 参数数组
     * @returns {Object} - 转换结果
     */
    static convertToSQLite(sql, params = []) {
        const parseResult = this.parseParameters(sql);
        
        if (!parseResult.hasParameters) {
            return { sql, params };
        }

        // 检查参数数量是否匹配
        if (parseResult.maxParamIndex > params.length) {
            throw new Error(
                `参数不足: SQL需要${parseResult.maxParamIndex}个参数，但只提供了${params.length}个`
            );
        }

        // 按出现顺序收集参数
        const orderedParams = [];
        const paramMap = new Map();
        let convertedSql = sql;
        
        // 按照在SQL中出现的顺序处理参数
        const matches = [...sql.matchAll(/\$(\d+)/g)];
        
        for (const match of matches) {
            const paramIndex = parseInt(match[1]);
            const paramValue = params[paramIndex - 1]; // $1对应数组索引0
            
            if (!paramMap.has(paramIndex)) {
                paramMap.set(paramIndex, orderedParams.length);
                orderedParams.push(paramValue);
            }
        }

        // 替换所有 $n 为 ?
        convertedSql = convertedSql.replace(/\$\d+/g, '?');

        return {
            sql: convertedSql,
            params: orderedParams
        };
    }

    /**
     * 将SQLite风格的SQL转换为PostgreSQL风格
     * @param {string} sql - 原始SQL (使用?占位符)
     * @param {Array} params - 参数数组
     * @returns {Object} - 转换结果
     */
    static convertToPostgreSQL(sql, params = []) {
        // 计算?占位符的数量
        const questionMarks = (sql.match(/\?/g) || []).length;
        
        if (questionMarks === 0) {
            return { sql, params };
        }

        // 检查参数数量是否匹配
        if (questionMarks > params.length) {
            throw new Error(
                `参数不足: SQL需要${questionMarks}个参数，但只提供了${params.length}个`
            );
        }

        // 将?依次替换为$1, $2, $3...
        let paramIndex = 1;
        const convertedSql = sql.replace(/\?/g, () => `$${paramIndex++}`);

        return {
            sql: convertedSql,
            params: params.slice(0, questionMarks) // 只使用需要的参数
        };
    }

    /**
     * 验证参数完整性
     * @param {string} sql - SQL语句
     * @param {Array} params - 参数数组
     * @returns {Object} - 验证结果
     */
    static validateParameters(sql, params = []) {
        const parseResult = this.parseParameters(sql);
        
        const validation = {
            isValid: true,
            errors: [],
            warnings: [],
            paramCount: parseResult.maxParamIndex,
            providedCount: params.length
        };

        if (parseResult.hasParameters) {
            // 检查参数数量
            if (parseResult.maxParamIndex > params.length) {
                validation.isValid = false;
                validation.errors.push(
                    `参数不足: 需要${parseResult.maxParamIndex}个，提供${params.length}个`
                );
            }

            // 检查参数连续性
            const sortedNumbers = [...parseResult.paramNumbers].sort((a, b) => a - b);
            for (let i = 0; i < sortedNumbers.length - 1; i++) {
                if (sortedNumbers[i + 1] - sortedNumbers[i] > 1) {
                    validation.warnings.push(
                        `参数编号不连续: $${sortedNumbers[i]} 到 $${sortedNumbers[i + 1]}`
                    );
                }
            }

            // 检查是否从$1开始
            if (sortedNumbers.length > 0 && sortedNumbers[0] !== 1) {
                validation.warnings.push(
                    `参数编号不从$1开始: 第一个参数是$${sortedNumbers[0]}`
                );
            }
        }

        return validation;
    }

    /**
     * 为调试提供详细的参数信息
     * @param {string} sql - SQL语句
     * @param {Array} params - 参数数组
     * @returns {Object} - 调试信息
     */
    static getDebugInfo(sql, params = []) {
        const parseResult = this.parseParameters(sql);
        const validation = this.validateParameters(sql, params);
        const sqliteResult = parseResult.hasParameters ? 
            this.convertToSQLite(sql, params) : { sql, params };

        // 检测SQL使用的占位符类型
        const hasPgStyle = parseResult.hasParameters;
        const hasSqliteStyle = (sql.match(/\?/g) || []).length > 0;
        
        let postgresqlResult;
        if (hasSqliteStyle && !hasPgStyle) {
            // 如果是SQLite风格，转换为PostgreSQL
            postgresqlResult = this.convertToPostgreSQL(sql, params);
        } else {
            // 已经是PostgreSQL风格或无参数
            postgresqlResult = { sql, params };
        }

        return {
            original: {
                sql,
                params,
                paramCount: params.length
            },
            parsed: parseResult,
            validation,
            converted: {
                sqlite: sqliteResult,
                postgresql: postgresqlResult
            }
        };
    }
}

module.exports = SqlParameterUtils;