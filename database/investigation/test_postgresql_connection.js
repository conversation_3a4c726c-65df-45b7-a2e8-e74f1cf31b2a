#!/usr/bin/env node
"use strict";

const PostgresAdapter = require("../adapters/postgresql");

async function testPostgreSQLConnection() {
    console.log("=".repeat(80));
    console.log("PostgreSQL 连接测试");
    console.log("=".repeat(80));
    console.log();

    // PostgreSQL配置
    const postgresConfig = {
        connection: process.env.DATABASE_URL || 'postgresql://postgres:<EMAIL>:48032/?directConnection=true'
    };

    console.log("PostgreSQL 连接配置:");
    console.log(`- 连接字符串: ${postgresConfig.connection.replace(/:[^:@]+@/, ':****@')}`);
    console.log();

    const DB = new PostgresAdapter(postgresConfig);
    
    try {
        console.log("尝试连接到 PostgreSQL...");
        await DB.connect();
        console.log("✅ PostgreSQL 连接成功！");
        console.log();

        // 测试基本查询
        console.log("执行测试查询...");
        const result = await DB.query("SELECT version() as version, current_database() as database, current_user as user");
        console.log("数据库信息:");
        console.log(`- PostgreSQL版本: ${result[0].version}`);
        console.log(`- 当前数据库: ${result[0].database}`);
        console.log(`- 当前用户: ${result[0].user}`);
        console.log();

        // 检查现有表
        console.log("检查现有表结构:");
        const tables = await DB.all(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        `);
        
        if (tables.length === 0) {
            console.log("❌ 当前数据库中没有表");
            console.log("提示: 这可能是一个空的PostgreSQL数据库，需要运行迁移来创建表结构");
        } else {
            console.log(`发现 ${tables.length} 个表:`);
            for (const table of tables) {
                console.log(`  - ${table.table_name}`);
            }
        }

        console.log();
        console.log("=".repeat(80));
        console.log("✅ PostgreSQL 连接测试完成");
        console.log("=".repeat(80));

    } catch (error) {
        console.error("❌ PostgreSQL 连接失败:");
        console.error(`错误类型: ${error.constructor.name}`);
        console.error(`错误信息: ${error.message}`);
        if (error.code) {
            console.error(`错误代码: ${error.code}`);
        }
        console.error();
        console.error("可能的原因:");
        console.error("1. 数据库服务器不可达");
        console.error("2. 用户名或密码错误");
        console.error("3. 数据库不存在");
        console.error("4. 网络连接问题");
        console.error("5. 防火墙阻止了连接");
    } finally {
        if (DB.pool) {
            await DB.disconnect();
        }
    }
}

// 执行测试
testPostgreSQLConnection().catch(console.error);