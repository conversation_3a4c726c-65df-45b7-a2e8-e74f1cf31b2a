#!/bin/bash
# 测试新的 ARM64 构建流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🧪 测试 ARM64 构建流程..."
echo ""

# 切换到项目根目录
cd "$(dirname "$0")/.."
PROJECT_ROOT=$(pwd)

log_info "项目目录: $PROJECT_ROOT"

# 步骤 1: 准备原生模块
log_info "步骤 1: 准备原生模块..."
if [ ! -f native-modules/linux-arm64/better_sqlite3.node ]; then
    ./arm-beta/scripts/prepare-native-modules.sh arm64 linux
else
    log_success "原生模块已存在"
fi

# 步骤 2: 构建二进制文件
log_info "步骤 2: 构建 ARM64 二进制文件..."
./arm-beta/scripts/build-arm.sh test

# 步骤 3: 验证二进制文件
log_info "步骤 3: 验证二进制文件..."
./arm-beta/scripts/verify-binary.sh

# 步骤 4: 打包部署文件
log_info "步骤 4: 创建部署包..."
./arm-beta/scripts/package-arm.sh test

# 显示结果
echo ""
log_success "构建测试完成！"
echo ""
log_info "生成的文件："
ls -lh dstatus-linux-arm64
ls -lh dstatus-arm64-beta-test.tar.gz
echo ""
log_info "下一步："
echo "1. 将 dstatus-arm64-beta-test.tar.gz 复制到 ARM64 系统"
echo "2. 解压并运行 ./start.sh 进行测试"
echo "3. 检查原生模块是否正常工作"