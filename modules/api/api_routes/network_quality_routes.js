'use strict';

const express = require('express');
const router = express.Router();

module.exports = (svr, monitor) => {
    const database = svr.locals.db;

    // 获取 featureChecker 实例
    function getFeatureChecker() {
        try {
            // 尝试从服务器实例获取
            if (svr && svr.locals && svr.locals['license-enhanced']) {
                return svr.locals['license-enhanced'].featureChecker;
            }

            // 后备方案：从全局应用实例获取
            if (global.app && global.app.locals && global.app.locals['license-enhanced']) {
                return global.app.locals['license-enhanced'].featureChecker;
            }

            console.error('[NetworkQuality API] 无法获取FeatureChecker实例');
            return null;
        } catch (error) {
            console.error('[NetworkQuality API] 无法获取FeatureChecker:', error);
            return null;
        }
    }

    // 获取 unifiedConfigService 实例
    function getUnifiedConfigService() {
        try {
            // 尝试从服务器实例获取
            if (svr && svr.locals && svr.locals['license-enhanced']) {
                return svr.locals['license-enhanced'].unifiedConfigService;
            }

            // 后备方案：从全局应用实例获取
            if (global.app && global.app.locals && global.app.locals['license-enhanced']) {
                return global.app.locals['license-enhanced'].unifiedConfigService;
            }

            console.error('[NetworkQuality API] 无法获取UnifiedConfigService实例');
            return null;
        } catch (error) {
            console.error('[NetworkQuality API] 无法获取UnifiedConfigService:', error);
            return null;
        }
    }

    // 时间范围映射（转换为秒）
    function getTimeRangeInSeconds(timeRange) {
        switch (timeRange) {
            case '1h':
                return 3600;
            case '6h':
                return 6 * 3600;
            case '24h':
                return 24 * 3600;
            case '7d':
                return 7 * 24 * 3600;
            case '30d':
                return 30 * 24 * 3600;
            default:
                return 24 * 3600; // 默认24小时
        }
    }

    // 格式化时间范围为可读格式
    function formatTimeRange(seconds) {
        if (seconds < 3600) {
            return `${Math.round(seconds / 60)}分钟`;
        } else if (seconds < 24 * 3600) {
            return `${Math.round(seconds / 3600)}小时`;
        } else {
            return `${Math.round(seconds / (24 * 3600))}天`;
        }
    }

    /**
     * 检查时间范围限制的中间件
     * @param {string} featureName - 功能名称
     * @returns {function} Express中间件函数
     */
    function checkTimeRangeLimit(featureName = 'NETWORK_QUALITY') {
        return async (req, res, next) => {
            try {
                // 从请求参数中获取时间范围
                const timeRange = req.query.timeRange || req.body.timeRange || '24h';
                const requestedTimeInSeconds = getTimeRangeInSeconds(timeRange);

                // 获取 unifiedConfigService 实例
                const unifiedConfigService = getUnifiedConfigService();
                if (!unifiedConfigService) {
                    console.error('[NetworkQuality API] UnifiedConfigService不可用，跳过时间范围检查');
                    return next(); // 服务不可用时不阻止访问
                }

                // 获取当前用户的套餐信息
                const featureChecker = getFeatureChecker();
                if (!featureChecker) {
                    console.error('[NetworkQuality API] FeatureChecker不可用，跳过时间范围检查');
                    return next(); // 服务不可用时不阻止访问
                }

                // 获取当前许可证信息以确定套餐名称
                const licenseInfo = featureChecker.getCurrentLicenseInfo();
                const planName = licenseInfo.planName || licenseInfo.planDisplayName || licenseInfo.planType;

                if (!planName) {
                    console.warn('[NetworkQuality API] 无法确定套餐名称，跳过时间范围检查');
                    return next(); // 无法确定套餐时不阻止访问
                }

                // 获取功能的时间范围限制
                const timeLimit = await unifiedConfigService.getFeatureTimeLimit(planName, featureName);
                
                // 如果没有配置限制，允许访问
                if (!timeLimit) {
                    return next();
                }

                // 检查请求的时间范围是否超出限制
                if (requestedTimeInSeconds > timeLimit) {
                    return res.status(403).json({
                        success: false,
                        error: 'TIME_RANGE_EXCEEDED',
                        errorType: 'PERMISSION_DENIED',
                        message: `当前套餐最多支持 ${formatTimeRange(timeLimit)} 的历史数据`,
                        data: {
                            requestedRange: timeRange,
                            requestedSeconds: requestedTimeInSeconds,
                            maxAllowedSeconds: timeLimit,
                            maxAllowedRange: formatTimeRange(timeLimit),
                            currentPlan: planName,
                            upgradeUrl: '/admin/license-management'
                        }
                    });
                }

                // 检查通过，继续处理请求
                next();
            } catch (error) {
                console.error('[NetworkQuality API] 时间范围检查失败:', error);
                // 错误时不阻止访问，但记录日志
                next();
            }
        };
    }

    /**
     * 获取所有监控目标的网络质量概览数据
     * GET /api/network-quality/overview?timeRange=<1h|6h|24h|7d|30d>
     */
    router.get('/api/network-quality/overview', async (req, res) => {
        try {
            const { timeRange = '24h' } = req.query;
            
            // 验证monitor对象
            if (!monitor || !monitor.targets || typeof monitor.targets.getAll !== 'function') {
                console.error('[Network Quality API] monitor对象不可用', {
                    monitor: !!monitor,
                    targets: !!monitor?.targets,
                    getAll: typeof monitor?.targets?.getAll
                });
                return res.status(500).json({
                    success: false,
                    message: 'Monitor服务不可用',
                    error: 'MONITOR_UNAVAILABLE'
                });
            }
            
            // 获取所有监控目标
            const targets = await monitor.targets.getAll();
            
            // 计算时间范围（秒）
            let durationSeconds;
            switch (timeRange) {
                case '1h':
                    durationSeconds = 60 * 60;
                    break;
                case '6h':
                    durationSeconds = 6 * 60 * 60;
                    break;
                case '24h':
                    durationSeconds = 24 * 60 * 60;
                    break;
                case '7d':
                    durationSeconds = 7 * 24 * 60 * 60;
                    break;
                case '30d':
                    durationSeconds = 30 * 24 * 60 * 60;
                    break;
                default:
                    durationSeconds = 24 * 60 * 60;
            }

            // 获取聚合网络质量数据
            const networkQualityData = {
                timeRange: timeRange,
                totalTargets: targets.length,
                summary: {
                    avgLatency: 0,
                    avgPacketLoss: 0,
                    avgAvailability: 0
                },
                targets: []
            };

            // 为所有监控目标获取网络质量数据
            for (const target of targets) {
                try {
                    // 获取监控目标的TCPing数据
                    const tcpingData = await getTargetTcpingData(target.id, durationSeconds, monitor);
                    
                    const targetQuality = {
                        id: target.id,
                        name: target.name,
                        host: target.host,
                        port: target.port,
                        region: target.region_name,
                        test_type: target.test_type,
                        metrics: calculateNetworkMetrics(tcpingData),
                        chartData: formatChartData(tcpingData, timeRange)
                    };
                    
                    networkQualityData.targets.push(targetQuality);
                } catch (error) {
                    console.error(`获取监控目标 ${target.id} 网络质量数据失败:`, error);
                    // 添加默认数据
                    networkQualityData.targets.push({
                        id: target.id,
                        name: target.name,
                        host: target.host,
                        port: target.port,
                        region: target.region_name,
                        test_type: target.test_type,
                        metrics: { avgLatency: 0, packetLoss: 0, availability: 0 },
                        chartData: { times: [], latencies: [] }
                    });
                }
            }

            // 计算总体统计
            if (networkQualityData.targets.length > 0) {
                const validTargets = networkQualityData.targets.filter(t => t.metrics.avgLatency > 0);
                
                if (validTargets.length > 0) {
                    const totalLatency = validTargets.reduce((sum, target) => 
                        sum + (target.metrics.avgLatency || 0), 0);
                    const totalPacketLoss = validTargets.reduce((sum, target) => 
                        sum + (target.metrics.packetLoss || 0), 0);
                    const totalAvailability = validTargets.reduce((sum, target) => 
                        sum + (target.metrics.availability || 0), 0);
                    
                    networkQualityData.summary.avgLatency = Math.round(totalLatency / validTargets.length);
                    networkQualityData.summary.avgPacketLoss = Math.round((totalPacketLoss / validTargets.length) * 100) / 100;
                    networkQualityData.summary.avgAvailability = Math.round((totalAvailability / validTargets.length) * 100) / 100;
                }
            }

            res.json({
                success: true,
                data: networkQualityData
            });
        } catch (error) {
            // 增强错误日志记录
            console.error('[Network Quality API] 获取网络质量概览数据失败');
            console.error('错误类型:', typeof error);
            console.error('错误对象:', error);
            console.error('错误消息:', error?.message || '无错误消息');
            console.error('错误堆栈:', error?.stack || '无堆栈信息');
            
            res.status(500).json({
                success: false,
                message: '获取网络质量数据失败: ' + (error?.message || '未知错误'),
                errorType: typeof error,
                errorDetails: error ? Object.keys(error).join(', ') : 'null or undefined'
            });
        }
    });

    /**
     * 获取特定监控目标的详细网络质量数据
     * GET /api/network-quality/target/:id?timeRange=<1h|6h|24h|7d|30d>&granularity=<m|5m|h|d|month>
     */
    router.get('/api/network-quality/target/:id', async (req, res) => {
        try {
            const { id } = req.params;
            const { timeRange = '24h', granularity } = req.query;
            
            // 验证监控目标是否存在
            if (!monitor || !monitor.targets || typeof monitor.targets.get !== 'function') {
                return res.status(500).json({
                    success: false,
                    message: 'Monitor服务不可用'
                });
            }
            
            const target = monitor.targets.get(id);
            
            if (!target) {
                return res.status(404).json({
                    success: false,
                    message: '监控目标不存在'
                });
            }

            // 计算时间范围（秒）
            let durationSeconds;
            switch (timeRange) {
                case '1h':
                    durationSeconds = 60 * 60;
                    break;
                case '6h':
                    durationSeconds = 6 * 60 * 60;
                    break;
                case '24h':
                    durationSeconds = 24 * 60 * 60;
                    break;
                case '7d':
                    durationSeconds = 7 * 24 * 60 * 60;
                    break;
                case '30d':
                    durationSeconds = 30 * 24 * 60 * 60;
                    break;
                default:
                    durationSeconds = 24 * 60 * 60;
            }

            // 获取TCPing数据，支持颗粒度参数
            const tcpingData = await getTargetTcpingDataWithGranularity(id, durationSeconds, granularity, timeRange, monitor);
            const metrics = calculateNetworkMetrics(tcpingData.data);
            const chartData = formatChartData(tcpingData.data, timeRange);

            res.json({
                success: true,
                data: {
                    target: {
                        id: target.id,
                        name: target.name,
                        host: target.host,
                        port: target.port,
                        region: target.region_name,
                        test_type: target.test_type
                    },
                    timeRange: timeRange,
                    granularity: tcpingData.actualGranularity,
                    dataSource: tcpingData.dataSource,
                    metrics: metrics,
                    chartData: chartData,
                    rawData: tcpingData.data.slice(-100), // 限制返回最近100条数据
                    metadata: {
                        dataPoints: tcpingData.data.length,
                        requestedGranularity: granularity || 'auto',
                        actualGranularity: tcpingData.actualGranularity,
                        dataSource: tcpingData.dataSource
                    }
                }
            });
        } catch (error) {
            // 增强错误日志记录
            console.error('[Network Quality API] 获取监控目标网络质量数据失败');
            console.error('错误类型:', typeof error);
            console.error('错误对象:', error);
            console.error('错误消息:', error?.message || '无错误消息');
            console.error('错误堆栈:', error?.stack || '无堆栈信息');
            
            res.status(500).json({
                success: false,
                message: '获取监控目标网络质量数据失败: ' + (error?.message || '未知错误'),
                errorType: typeof error,
                errorDetails: error ? Object.keys(error).join(', ') : 'null or undefined'
            });
        }
    });

    /**
     * 获取按节点分组的网络质量监控数据
     * GET /api/network-quality/nodes-overview?timeRange=<1h|6h|24h|7d|30d>
     */
    router.get('/api/network-quality/nodes-overview', async (req, res) => {
        try {
            const { timeRange = '24h' } = req.query;
            
            // 计算时间范围（秒）
            const durationSeconds = getTimeRangeDuration(timeRange);
            
            // 获取所有活跃的服务器/节点
            const allServers = await database.servers.all();
            const activeServers = allServers.filter(server => server.status === 1);
            
            // 获取所有分组信息
            const allGroups = await database.groups.all();
            const groupsMap = new Map(allGroups.map(group => [group.id, group.name]));
            
            // 获取所有监控目标
            let allTargets = [];
            if (monitor && monitor.targets && typeof monitor.targets.getAll === 'function') {
                try {
                    allTargets = await monitor.targets.getAll();
                    console.log('[Network Quality API] 获取监控目标成功，数量:', allTargets.length);
                } catch (targetsError) {
                    console.error('[Network Quality API] 获取监控目标失败:', targetsError);
                    throw targetsError;
                }
            } else {
                console.warn('[NetworkQuality API] Monitor.targets不可用，返回空数据');
                return res.json({
                    success: true,
                    data: {
                        summary: {
                            totalNodes: 0,
                            totalTargets: 0,
                            avgLatency: 0,
                            healthScore: 0
                        },
                        nodes: []
                    }
                });
            }
            
            // 按节点分组构建数据
            const nodesData = [];
            
            for (const server of activeServers) {
                const nodeId = server.sid;
                const nodeName = server.name;
                
                // 找到该节点监控的所有目标
                const nodeTargets = allTargets.filter(target => {
                    if (!target.node_id) return false;
                    
                    try {
                        // 尝试解析JSON格式的node_id
                        if (target.node_id.startsWith('[') || target.node_id.startsWith('{')) {
                            const nodeIds = JSON.parse(target.node_id);
                            return Array.isArray(nodeIds) ? nodeIds.includes(nodeId) : nodeIds === nodeId;
                        } else {
                            // 直接字符串比较
                            return target.node_id === nodeId;
                        }
                    } catch (e) {
                        // 如果解析失败，进行直接比较
                        return target.node_id === nodeId;
                    }
                });
                
                if (nodeTargets.length === 0) {
                    continue; // 跳过没有监控目标的节点
                }
                
                // 获取每个目标的数据
                const targetsData = [];
                let totalLatency = 0;
                let totalAvailability = 0;
                let validTargetsCount = 0;
                
                for (const target of nodeTargets) {
                    try {
                        // 获取该节点对该目标的TCPing数据
                        const tcpingData = await getTargetTcpingDataByNode(target.id, nodeId, durationSeconds, monitor);
                        const metrics = calculateNetworkMetrics(tcpingData);
                        const chartData = formatChartData(tcpingData, timeRange);
                        
                        const targetData = {
                            targetId: target.id,
                            targetName: target.name,
                            host: target.host,
                            port: target.port,
                            region: target.region_name,
                            testType: target.test_type || 'tcping',
                            metrics: metrics,
                            chartData: chartData,
                            dataPoints: tcpingData.length
                        };
                        
                        targetsData.push(targetData);
                        
                        // 累计计算节点级别指标
                        if (metrics.avgLatency > 0) {
                            totalLatency += metrics.avgLatency;
                            totalAvailability += metrics.availability;
                            validTargetsCount++;
                        }
                    } catch (error) {
                        console.error(`获取节点 ${nodeId} 目标 ${target.id} 数据失败:`, error);
                        // 添加空数据占位
                        targetsData.push({
                            targetId: target.id,
                            targetName: target.name,
                            host: target.host,
                            port: target.port,
                            region: target.region_name,
                            testType: target.test_type || 'tcping',
                            metrics: { avgLatency: 0, packetLoss: 100, availability: 0 },
                            chartData: { times: [], latencies: [], packetLoss: [] },
                            dataPoints: 0
                        });
                    }
                }
                
                // 计算节点级别的整体指标
                const nodeMetrics = {
                    avgLatency: validTargetsCount > 0 ? Math.round(totalLatency / validTargetsCount) : 0,
                    avgAvailability: validTargetsCount > 0 ? Math.round(totalAvailability / validTargetsCount) : 0,
                    totalTargets: nodeTargets.length,
                    activeTargets: validTargetsCount,
                    healthScore: validTargetsCount > 0 ? Math.round(totalAvailability / validTargetsCount) : 0
                };
                
                // 提取分组和地区信息
                const groupId = server.group_id || 'default';
                let groupName = groupsMap.get(groupId);
                
                // 如果在groups表中找不到对应的分组，使用友好的默认名称
                if (!groupName) {
                    groupName = groupId === 'default' ? '默认分组' : '未分类分组';
                }
                
                const serverData = server.data || {};
                const location = serverData.location || {};

                // 严格从数据库中获取地区信息，不进行推断
                let regionName = '未知地区';
                let countryData = {};

                // 从存储的location数据获取
                if (location.name_zh) {
                    // 新的数据结构：直接从location获取
                    regionName = location.name_zh;

                    // 如果name_zh是"未知(XX)"格式，尝试使用正确的中文名称
                    if (regionName.startsWith('未知(') && regionName.endsWith(')')) {
                        const code = regionName.slice(3, -1); // 提取括号中的国家代码
                        const correctName = getCountryNameZhLocal(code);
                        console.log(`[DEBUG] 地区名称修正: ${regionName} -> ${correctName} (代码: ${code})`);
                        if (correctName !== regionName) {
                            regionName = correctName;
                        }
                    }

                    countryData = {
                        code: location.code || 'UNKNOWN',
                        name_zh: regionName,
                        name: location.country_name || regionName,
                        flag: location.flag || ''
                    };
                } else if (location.code) {
                    // 如果有国家代码，转换为中文名称
                    regionName = getCountryNameZh(location.code);

                    // 如果country_name是"Unknown"且有正确的中文名称，使用中文名称
                    let countryName = location.country_name || regionName;
                    if (countryName === 'Unknown' && regionName !== `未知(${location.code})`) {
                        countryName = regionName;
                    }

                    countryData = {
                        code: location.code,
                        name_zh: regionName,
                        name: countryName,
                        flag: location.flag || ''
                    };
                } else if (location.country && location.country.name_zh) {
                    // 旧的数据结构：从location.country获取
                    regionName = location.country.name_zh;
                    countryData = location.country;
                } else if (location.country && location.country.name) {
                    regionName = location.country.name;
                    countryData = location.country;
                } else {
                    // 如果没有存储的地区信息，保持为未知地区
                    regionName = '未知地区';
                    countryData = {};
                }

                nodesData.push({
                    nodeId: nodeId,
                    nodeName: nodeName,
                    nodeStatus: server.status,
                    groupId: groupId,
                    groupName: groupName,
                    location: {
                        country: countryData,
                        region: regionName
                    },
                    targets: targetsData,
                    nodeMetrics: nodeMetrics
                });
            }
            
            // 计算全局统计
            let globalTotalLatency = 0;
            let globalTotalAvailability = 0;
            let globalValidNodes = 0;
            let globalTotalTargets = 0;
            
            nodesData.forEach(node => {
                if (node.nodeMetrics.activeTargets > 0) {
                    globalTotalLatency += node.nodeMetrics.avgLatency;
                    globalTotalAvailability += node.nodeMetrics.avgAvailability;
                    globalValidNodes++;
                }
                globalTotalTargets += node.nodeMetrics.totalTargets;
            });
            
            const summary = {
                totalNodes: nodesData.length,
                totalTargets: globalTotalTargets,
                avgLatency: globalValidNodes > 0 ? Math.round(globalTotalLatency / globalValidNodes) : 0,
                avgAvailability: globalValidNodes > 0 ? Math.round(globalTotalAvailability / globalValidNodes) : 0,
                healthScore: globalValidNodes > 0 ? Math.round(globalTotalAvailability / globalValidNodes) : 0
            };

            res.json({
                success: true,
                data: {
                    timeRange: timeRange,
                    summary: summary,
                    nodes: nodesData,
                    lastUpdated: Date.now()
                }
            });
        } catch (error) {
            // 增强错误日志记录
            console.error('[Network Quality API] 获取节点网络质量概览数据失败');
            console.error('错误类型:', typeof error);
            console.error('错误对象:', error);
            console.error('错误消息:', error?.message || '无错误消息');
            console.error('错误堆栈:', error?.stack || '无堆栈信息');
            console.error('错误字符串:', String(error));
            
            // 如果error是字符串，直接使用
            const errorMessage = typeof error === 'string' ? error : (error?.message || '未知错误');
            
            res.status(500).json({
                success: false,
                message: '获取节点网络质量数据失败: ' + errorMessage,
                errorType: typeof error,
                errorDetails: error ? Object.keys(error).join(', ') : 'null or undefined'
            });
        }
    });

    /**
     * 获取网络质量统计概要
     * GET /api/network-quality/stats
     */
    router.get('/api/network-quality/stats', async (req, res) => {
        try {
            // 验证monitor对象
            if (!monitor || !monitor.targets || typeof monitor.targets.getAll !== 'function') {
                console.error('[Network Quality API] monitor对象不可用 (统计API)');
                return res.status(500).json({
                    success: false,
                    message: 'Monitor服务不可用',
                    error: 'MONITOR_UNAVAILABLE'
                });
            }
            
            const targets = await monitor.targets.getAll();
            
            // 获取最近1小时的数据进行快速统计
            const durationSeconds = 60 * 60; // 1小时
            
            let totalLatency = 0;
            let totalPacketLoss = 0;
            let totalAvailability = 0;
            let validTargetCount = 0;

            for (const target of targets) {
                try {
                    const tcpingData = await getTargetTcpingData(target.id, durationSeconds, monitor);
                    if (tcpingData.length > 0) {
                        const metrics = calculateNetworkMetrics(tcpingData);
                        totalLatency += metrics.avgLatency || 0;
                        totalPacketLoss += metrics.packetLoss || 0;
                        totalAvailability += metrics.availability || 0;
                        validTargetCount++;
                    }
                } catch (error) {
                    console.error(`统计监控目标 ${target.id} 网络质量失败:`, error);
                }
            }

            const stats = {
                totalTargets: targets.length,
                activeTargets: validTargetCount,
                avgLatency: validTargetCount > 0 ? Math.round(totalLatency / validTargetCount) : 0,
                avgPacketLoss: validTargetCount > 0 ? Math.round((totalPacketLoss / validTargetCount) * 100) / 100 : 0,
                avgAvailability: validTargetCount > 0 ? Math.round((totalAvailability / validTargetCount) * 100) / 100 : 0,
                lastUpdated: Date.now()
            };

            res.json({
                success: true,
                data: stats
            });
        } catch (error) {
            // 增强错误日志记录
            console.error('[Network Quality API] 获取网络质量统计失败');
            console.error('错误类型:', typeof error);
            console.error('错误对象:', error);
            console.error('错误消息:', error?.message || '无错误消息');
            console.error('错误堆栈:', error?.stack || '无堆栈信息');
            
            res.status(500).json({
                success: false,
                message: '获取网络质量统计失败: ' + (error?.message || '未知错误'),
                errorType: typeof error,
                errorDetails: error ? Object.keys(error).join(', ') : 'null or undefined'
            });
        }
    });

    return router;
};

/**
 * 获取监控目标的TCPing数据
 */
async function getTargetTcpingData(targetId, durationSeconds, monitor) {
    try {
        let data = [];

        // 验证monitor对象
        if (!monitor) {
            console.error('[getTargetTcpingData] monitor对象为null或undefined');
            return [];
        }

        // 根据时间范围选择合适的数据粒度
        if (durationSeconds <= 2 * 60 * 60) { // 2小时内使用分钟级数据
            if (monitor.tcping_m && typeof monitor.tcping_m.selectByTimeRange === 'function') {
                data = await monitor.tcping_m.selectByTimeRange(targetId, durationSeconds);
            } else {
                console.warn('[getTargetTcpingData] tcping_m.selectByTimeRange不可用');
            }
        } else if (durationSeconds <= 7 * 24 * 60 * 60) { // 7天内使用小时级数据
            if (monitor.tcping_h && typeof monitor.tcping_h.selectByTimeRange === 'function') {
                data = await monitor.tcping_h.selectByTimeRange(targetId, durationSeconds);
            } else {
                console.warn('[getTargetTcpingData] tcping_h.selectByTimeRange不可用');
            }
        } else { // 更长时间使用天级数据
            if (monitor.tcping_d && typeof monitor.tcping_d.selectByTimeRange === 'function') {
                data = await monitor.tcping_d.selectByTimeRange(targetId, durationSeconds);
            } else {
                console.warn('[getTargetTcpingData] tcping_d.selectByTimeRange不可用');
            }
        }

        return Array.isArray(data) ? data : [];
    } catch (error) {
        console.error('[getTargetTcpingData] 获取TCPing数据失败:', error);
        console.error('错误类型:', typeof error);
        console.error('错误消息:', error?.message);
        console.error('错误堆栈:', error?.stack);
        return [];
    }
}

/**
 * 获取监控目标的TCPing数据（支持颗粒度参数）
 */
async function getTargetTcpingDataWithGranularity(targetId, durationSeconds, granularity, timeRange, monitor) {
    try {
        let data = [];
        let actualGranularity = granularity;
        let dataSource = '';

        // 如果未指定颗粒度，根据时间范围自动选择
        if (!granularity) {
            if (timeRange === '1h' || timeRange === '6h') {
                actualGranularity = 'm';
            } else if (timeRange === '24h') {
                actualGranularity = '5m';
            } else if (timeRange === '7d') {
                actualGranularity = 'h';
            } else if (timeRange === '30d') {
                actualGranularity = 'd';
            } else {
                actualGranularity = 'h';
            }
        }

        // 验证monitor对象
        if (!monitor) {
            console.error('[getTargetTcpingDataWithGranularity] monitor对象为null或undefined');
            return {
                data: [],
                actualGranularity: actualGranularity,
                dataSource: 'error'
            };
        }
        
        // 根据颗粒度选择对应的数据表
        switch (actualGranularity) {
            case 'm':
                if (monitor.tcping_m && typeof monitor.tcping_m.selectByTimeRange === 'function') {
                    data = await monitor.tcping_m.selectByTimeRange(targetId, durationSeconds) || [];
                }
                dataSource = 'tcping_m';
                break;
            case '5m':
                // 如果有5分钟表就用5分钟表，否则用分钟表
                if (monitor.tcping_5m && typeof monitor.tcping_5m.selectByTimeRange === 'function') {
                    data = await monitor.tcping_5m.selectByTimeRange(targetId, durationSeconds) || [];
                    dataSource = 'tcping_5m';
                } else if (monitor.tcping_m && typeof monitor.tcping_m.selectByTimeRange === 'function') {
                    data = await monitor.tcping_m.selectByTimeRange(targetId, durationSeconds) || [];
                    dataSource = 'tcping_m';
                    actualGranularity = 'm'; // 实际使用的是分钟级
                }
                break;
            case 'h':
                if (monitor.tcping_h && typeof monitor.tcping_h.selectByTimeRange === 'function') {
                    data = await monitor.tcping_h.selectByTimeRange(targetId, durationSeconds) || [];
                }
                dataSource = 'tcping_h';
                break;
            case 'd':
                if (monitor.tcping_d && typeof monitor.tcping_d.selectByTimeRange === 'function') {
                    data = await monitor.tcping_d.selectByTimeRange(targetId, durationSeconds) || [];
                }
                dataSource = 'tcping_d';
                break;
            case 'month':
                if (monitor.tcping_month && typeof monitor.tcping_month.selectByTimeRange === 'function') {
                    data = await monitor.tcping_month.selectByTimeRange(targetId, durationSeconds) || [];
                }
                dataSource = 'tcping_month';
                break;
            default:
                // 回退到自动选择
                if (durationSeconds <= 2 * 60 * 60) {
                    if (monitor.tcping_m && typeof monitor.tcping_m.selectByTimeRange === 'function') {
                        data = await monitor.tcping_m.selectByTimeRange(targetId, durationSeconds) || [];
                    }
                    dataSource = 'tcping_m';
                    actualGranularity = 'm';
                } else if (durationSeconds <= 7 * 24 * 60 * 60) {
                    if (monitor.tcping_h && typeof monitor.tcping_h.selectByTimeRange === 'function') {
                        data = await monitor.tcping_h.selectByTimeRange(targetId, durationSeconds) || [];
                    }
                    dataSource = 'tcping_h';
                    actualGranularity = 'h';
                } else {
                    if (monitor.tcping_d && typeof monitor.tcping_d.selectByTimeRange === 'function') {
                        data = await monitor.tcping_d.selectByTimeRange(targetId, durationSeconds) || [];
                    }
                    dataSource = 'tcping_d';
                    actualGranularity = 'd';
                }
        }

        console.log(`[Network Quality] 获取目标 ${targetId} 数据: ${dataSource} (${actualGranularity}), ${data.length} 条记录`);

        return {
            data: Array.isArray(data) ? data : [],
            actualGranularity: actualGranularity,
            dataSource: dataSource
        };
    } catch (error) {
        console.error('获取TCPing数据失败:', error);
        return {
            data: [],
            actualGranularity: granularity || 'h',
            dataSource: 'error'
        };
    }
}

/**
 * 计算网络质量指标
 */
function calculateNetworkMetrics(tcpingData) {
    if (!Array.isArray(tcpingData) || tcpingData.length === 0) {
        return {
            avgLatency: 0,
            packetLoss: 0,
            availability: 0,
            minLatency: 0,
            maxLatency: 0
        };
    }

    let totalLatency = 0;
    let totalMinLatency = 0;
    let totalMaxLatency = 0;
    let totalSuccessRate = 0;
    let validRecords = 0;

    tcpingData.forEach(record => {
        if (record && typeof record.avg_time === 'number') {
            totalLatency += record.avg_time;
            totalMinLatency += record.min_time || 0;
            totalMaxLatency += record.max_time || 0;
            totalSuccessRate += record.success_rate || 0;
            validRecords++;
        }
    });

    if (validRecords === 0) {
        return {
            avgLatency: 0,
            packetLoss: 100,
            availability: 0,
            minLatency: 0,
            maxLatency: 0
        };
    }

    const avgLatency = Math.round(totalLatency / validRecords);
    const avgSuccessRate = totalSuccessRate / validRecords;
    const packetLoss = Math.round((1 - avgSuccessRate) * 10000) / 100; // 转换为百分比
    const availability = Math.round(avgSuccessRate * 10000) / 100; // 转换为百分比

    return {
        avgLatency: avgLatency,
        packetLoss: packetLoss,
        availability: availability,
        minLatency: Math.round(totalMinLatency / validRecords),
        maxLatency: Math.round(totalMaxLatency / validRecords)
    };
}

/**
 * 获取时间范围对应的秒数
 */
function getTimeRangeDuration(timeRange) {
    switch (timeRange) {
        case '1h':
            return 60 * 60;
        case '6h':
            return 6 * 60 * 60;
        case '24h':
            return 24 * 60 * 60;
        case '7d':
            return 7 * 24 * 60 * 60;
        case '30d':
            return 30 * 24 * 60 * 60;
        default:
            return 24 * 60 * 60;
    }
}

/**
 * 获取特定节点对特定监控目标的TCPing数据
 */
async function getTargetTcpingDataByNode(targetId, nodeId, durationSeconds, monitor) {
    try {
        let data = [];

        // 验证monitor对象
        if (!monitor) {
            console.error('[getTargetTcpingDataByNode] monitor对象为null或undefined');
            return [];
        }

        // 根据时间范围选择合适的数据粒度
        if (durationSeconds <= 2 * 60 * 60) { // 2小时内使用分钟级数据
            if (monitor.tcping_m && typeof monitor.tcping_m.selectByTimeRangeAndNode === 'function') {
                data = await monitor.tcping_m.selectByTimeRangeAndNode(targetId, nodeId, durationSeconds);
            } else {
                console.warn('[getTargetTcpingDataByNode] tcping_m.selectByTimeRangeAndNode不可用');
            }
        } else if (durationSeconds <= 7 * 24 * 60 * 60) { // 7天内使用小时级数据
            if (monitor.tcping_h && typeof monitor.tcping_h.selectByTimeRangeAndNode === 'function') {
                data = await monitor.tcping_h.selectByTimeRangeAndNode(targetId, nodeId, durationSeconds);
            } else {
                console.warn('[getTargetTcpingDataByNode] tcping_h.selectByTimeRangeAndNode不可用');
            }
        } else { // 更长时间使用天级数据
            if (monitor.tcping_d && typeof monitor.tcping_d.selectByTimeRangeAndNode === 'function') {
                data = await monitor.tcping_d.selectByTimeRangeAndNode(targetId, nodeId, durationSeconds);
            } else {
                console.warn('[getTargetTcpingDataByNode] tcping_d.selectByTimeRangeAndNode不可用');
            }
        }

        return Array.isArray(data) ? data : [];
    } catch (error) {
        console.error(`[getTargetTcpingDataByNode] 获取节点 ${nodeId} 对目标 ${targetId} 的TCPing数据失败:`, error);
        console.error('错误类型:', typeof error);
        console.error('错误消息:', error?.message);
        console.error('错误堆栈:', error?.stack);
        return [];
    }
}

/**
 * 格式化图表数据 - 优化版本，智能采样减少数据点
 */
function formatChartData(tcpingData, timeRange) {
    if (!Array.isArray(tcpingData) || tcpingData.length === 0) {
        return {
            times: [],
            latencies: [],
            packetLoss: []
        };
    }

    // 根据时间范围决定最大数据点数量（考虑渲染性能）
    let maxDataPoints;
    switch (timeRange) {
        case '1h':
            maxDataPoints = 60;  // 1分钟一个点
            break;
        case '6h':
            maxDataPoints = 72;  // 5分钟一个点
            break;
        case '24h':
            maxDataPoints = 96;  // 15分钟一个点
            break;
        case '7d':
            maxDataPoints = 168; // 1小时一个点
            break;
        case '30d':
            maxDataPoints = 120; // 6小时一个点
            break;
        default:
            maxDataPoints = 100;
    }

    // 如果数据点少于阈值，直接返回
    if (tcpingData.length <= maxDataPoints) {
        const times = [];
        const latencies = [];
        const packetLoss = [];

        tcpingData.forEach(record => {
            if (record && record.created_at) {
                times.push(new Date(record.created_at * 1000).toISOString());
                latencies.push(record.avg_time > 0 ? record.avg_time : null);
                packetLoss.push(Math.round((1 - (record.success_rate || 0)) * 100));
            }
        });

        return { times, latencies, packetLoss };
    }

    // 智能采样：使用平均值聚合而不是简单跳过
    const chunkSize = Math.ceil(tcpingData.length / maxDataPoints);
    const sampledData = [];

    for (let i = 0; i < tcpingData.length; i += chunkSize) {
        const chunk = tcpingData.slice(i, i + chunkSize);
        if (chunk.length === 0) continue;

        // 计算该时间段的平均值
        let totalLatency = 0;
        let totalSuccessRate = 0;
        let validCount = 0;
        let latestTimestamp = 0;

        chunk.forEach(record => {
            if (record && record.created_at) {
                totalLatency += record.avg_time || 0;
                totalSuccessRate += record.success_rate || 0;
                validCount++;
                latestTimestamp = Math.max(latestTimestamp, record.created_at);
            }
        });

        if (validCount > 0) {
            sampledData.push({
                created_at: latestTimestamp,
                avg_time: Math.round(totalLatency / validCount),
                success_rate: totalSuccessRate / validCount
            });
        }
    }

    const times = [];
    const latencies = [];
    const packetLoss = [];

    sampledData.forEach(record => {
        times.push(new Date(record.created_at * 1000).toISOString());
        latencies.push(record.avg_time > 0 ? record.avg_time : null);
        packetLoss.push(Math.round((1 - record.success_rate) * 100));
    });

    console.log(`[数据采样] ${timeRange}: 原始${tcpingData.length}点 -> 采样${sampledData.length}点`);

    return {
        times: times,
        latencies: latencies,
        packetLoss: packetLoss
    };
}



/**
 * 获取国家中文名（本地版本）
 */
function getCountryNameZhLocal(countryCode) {
    const countryMap = {
        'CN': '中国',
        'HK': '香港',
        'TW': '台湾',
        'JP': '日本',
        'KR': '韩国',
        'SG': '新加坡',
        'US': '美国',
        'CA': '加拿大',
        'GB': '英国',
        'UK': '英国',
        'DE': '德国',
        'FR': '法国',
        'AU': '澳大利亚',
        'VN': '越南',
        'TH': '泰国',
        'MY': '马来西亚',
        'ID': '印度尼西亚',
        'PH': '菲律宾',
        'RU': '俄罗斯',
        'UA': '乌克兰',
        'BR': '巴西',
        'IN': '印度',
        'ZA': '南非',
        'NL': '荷兰',
        'IT': '意大利',
        'ES': '西班牙',
        'CH': '瑞士',
        'SE': '瑞典',
        'NO': '挪威',
        'DK': '丹麦',
        'FI': '芬兰',
        'LO': '本地网络',
        'OT': '其他地区'
    };

    return countryMap[countryCode] || `未知(${countryCode})`;
}

/**
 * 获取国家中文名
 */
function getCountryNameZh(countryCode) {
    return getCountryNameZhLocal(countryCode);
}